use std::{collections::HashMap, sync::Arc};
use alloy::{primitives::{utils, Address, U256}, sol};
use crate::{connector::Connector, vira::{consts::{UPDATE_BALANCE_RETRY}, errors::{self, DEXError}, consts::U_900005}, CONFIG};
use tokio::sync::RwLock;

sol! (
    #[derive(Debug)]
    #[allow(missing_docs)]
    #[sol(rpc)]
    IVira,
    "src/vira/contract/ViraLogic.json"
);

sol! {
    #[derive(Debug)]
    #[sol(rpc)]
    interface IERC20 {
        function balanceOf(address account) external view returns (uint256);
    }
}

pub struct Contract {
    pub owner : Address,
    pub addr : Address,
    pub connector : Arc<Connector>,

    pub balance : RwLock<HashMap<Address, U256>>,
}

impl Clone for Contract {
    fn clone(&self) -> Self {
        Contract {
            owner: self.owner,
            addr: self.addr,
            connector: self.connector.clone(),
            balance: RwLock::new(HashMap::new()), // 创建新的空余额映射
        }
    }
}

impl Contract {

    pub fn new(address : Address, connector : Arc<Connector>) -> Contract {
        Contract {
            owner : <Address as std::str::FromStr>::from_str("0xF109A1D7f1bDD87F7251637D27D8c30DA7E07e7F").unwrap(),
            //owner : Address::ZERO,
            addr : address,
            connector : connector,
            balance : RwLock::new(HashMap::new())
        }
    }

    //更新stable余额和owner
    pub async fn update(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let contract = IVira::new(self.addr, self.connector.provider());
        self.owner = contract.owner().call().await?;
        println!("合约所有者已更新: {}", self.owner);
        println!("stable balances:");
        // 遍历所有稳定币地址
        for &stable_addr in CONFIG.stables.iter() {
            // 创建ERC20合约实例
            let token = IERC20::new(stable_addr, self.connector.provider());
            // 调用合约获取余额
            // Retry the balance fetching in case of an error
            let mut attempts = 0;
            let mut success = false;
            
            while attempts < UPDATE_BALANCE_RETRY && !success {
                attempts += 1;
                match token.balanceOf(self.addr).call().await {
                    Ok(result) => {
                        let mut balance = self.balance.write().await;
                        balance.insert(stable_addr, result);
                        if let Some(t) = CONFIG.tokens.get(&stable_addr) {
                            println!(" ({}) {}", t.name, utils::format_units(result, t.decimals).unwrap());
                        }
                        success = true;
                    },
                    Err(e) => {
                        println!("Attempt {}: Failed to get balance for stable token {}: {:?}", attempts, stable_addr, e);
                        if attempts == UPDATE_BALANCE_RETRY {
                            println!("Exceeded maximum retry attempts for stable token {}", stable_addr);
                            panic!("update balance error");
                        } else {
                            // Wait before retrying
                            tokio::time::sleep(tokio::time::Duration::from_secs(3)).await;
                        }
                    }
                }
            }
        }
        Ok(())
    }

    pub async fn get_balance(&self) -> HashMap<Address, U256> {
        let balance = self.balance.read().await;
        balance.clone()
    }

    pub async fn get_balance_by_addr(&self, addr: &Address) -> Option<U256> {
        let balance = self.balance.read().await;
        balance.get(addr).copied()
    }

    // 提取获取stable_addrs和stble_amounts的共同逻辑到一个私有方法中
    async fn get_stable_addrs_and_amounts(&self, split: Option<usize>) -> (Vec<Address>, Vec<U256>) {
        let mut balances = self.get_balance().await;
        let split = split.unwrap_or(1);

        //把balances所有的值除以BATCH_SIZE, 保证check期间不会出现stable为0的情况
        for v in balances.values_mut() {
            *v /= U256::from(split);
        }
        //检查balances是否有值为0的情况
        for v in balances.values() {
            if *v == U256::ZERO {
                panic!("balances has zero value");
            }
        }
        
        // 把balances的keys和values分别生成两个新的vec!
        let stable_addrs: Vec<_> = balances.keys().cloned().collect();
        let stable_amounts: Vec<_> = balances.values().cloned().collect();

        (stable_addrs, stable_amounts)
    }

    pub async fn batch_check_pair_fee(&self, inputs : Vec<ViraLogic::CheckPairFeeInputDesc>) -> Result<Vec<Vec<U256>>, DEXError>{
        let contract = IVira::new(self.addr, self.connector.provider());
        let batch_size = inputs.len();
        let (stable_addrs, stable_amounts) = self.get_stable_addrs_and_amounts(Some(batch_size)).await;

        // 先尝试批量请求
        match contract.batchCheckPairFee(inputs.clone(), stable_addrs.clone(), stable_amounts.clone()).from(self.owner).call().await {
            Ok(res) => Ok(res),
            Err(e) => {
                println!("Batch request failed: {:?}, retrying with individual requests", e);
                //停顿2秒
                tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;
                // 批量请求失败，改为逐个请求
                let mut all_fees = Vec::new();
                for input in inputs {
                    let addr = &input.pair[0].addr.clone();
                    match contract.batchCheckPairFee(vec![input], stable_addrs.clone(), stable_amounts.clone()).from(self.owner).call().await {
                        Ok(res) => all_fees.extend(res),
                        Err(e) => {
                            println!("Individual request failed: {}, {:?}", addr, e);
                            // - 900005: 未知原因
                            all_fees.push(vec![U_900005]);
                        }
                    }
                }
                Ok(all_fees)
            }
        }
    }
    
    /// 批量检查MEV路径的费用和状态，支持降级机制
    ///
    /// 该函数首先尝试批量处理所有MEV请求，如果批量处理失败，
    /// 会自动降级为单个请求逐一处理，确保系统的容错性。
    ///
    /// # 参数
    /// * `inputs` - MEV请求数组，每个元素是一个 Vec<ViraData::PoolReq>
    ///
    /// # 返回值
    /// * `Result<Vec<ViraLogic::CheckMevsResultDesc>, DEXError>` - 成功时返回检查结果数组
    ///
    /// # 降级机制
    /// 1. 首先尝试批量处理所有请求
    /// 2. 如果批量处理失败，记录错误并启动降级机制
    /// 3. 将批量请求拆分为单个请求，逐一处理
    /// 4. 收集所有单个请求的结果，合并返回
    /// 5. 对于单个请求失败的情况，创建空结果以保持数组长度一致
    pub async fn batch_check_mev(&self, inputs : Vec<Vec<ViraData::PoolReq>>) -> Result<Vec<ViraLogic::CheckMevsResultDesc>, DEXError>{
        let batch_size = inputs.len();
        let contract = IVira::new(self.addr, self.connector.provider());
        let (stable_addrs, stable_amounts) = self.get_stable_addrs_and_amounts(Some(batch_size)).await;

        // 首先尝试批量处理
        match contract.batchCheckMev(inputs.clone(), stable_addrs.clone(), stable_amounts.clone()).from(self.owner).call().await {
            Ok(res) => {
                //println!("✅ 批量MEV检查成功，处理了 {} 个MEV路径", batch_size);
                Ok(res)
            }
            Err(e) => {
                println!("❌ 批量MEV检查失败，启动降级机制: {:?}", e);
                println!("   - 合约地址: {}", self.addr);
                println!("   - 批次大小: {}", batch_size);
                println!("   - 正在将批量请求拆分为单个请求进行处理...");

                // 等待2秒后开始降级处理
                tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;

                // 降级机制：逐个处理每个MEV请求
                let mut all_results = Vec::with_capacity(batch_size);
                let mut success_count = 0;
                let mut failed_count = 0;

                for (index, single_input) in inputs.into_iter().enumerate() {

                    match contract.batchCheckMev(
                        vec![single_input],
                        stable_addrs.clone(),
                        stable_amounts.clone()
                    ).from(self.owner).call().await {
                        Ok(mut results) => {
                            if !results.is_empty() {
                                all_results.push(results.remove(0));
                                success_count += 1;
                            } else {
                                // 合约返回空结果，创建默认空结果
                                all_results.push(ViraLogic::CheckMevsResultDesc {
                                    gas: U256::ZERO,
                                    fee0: vec![],
                                    fee1: vec![],
                                });
                                failed_count += 1;
                                println!("   - 单个请求 {}/{}: 合约返回空结果", index + 1, batch_size);
                            }
                        }
                        Err(single_e) => {
                            // 单个请求也失败，创建空结果以保持数组长度一致
                            all_results.push(ViraLogic::CheckMevsResultDesc {
                                gas: U256::ZERO,
                                fee0: vec![],
                                fee1: vec![],
                            });
                            failed_count += 1;
                            println!("   - 单个请求 {}/{} 失败: {:?}", index + 1, batch_size, single_e);
                        }
                    }

                    // 每处理10个请求显示一次进度
                    if (index + 1) % 10 == 0 || index + 1 == batch_size {
                        println!("   - 降级处理进度: {}/{} (成功: {}, 失败: {})",
                            index + 1, batch_size, success_count, failed_count);
                    }
                }

                println!("✅ 降级机制完成，总计处理: {} 个请求 (成功: {}, 失败: {})",
                    batch_size, success_count, failed_count);

                Ok(all_results)
            }
        }
    }
}

mod tests {
    use crate::vira::Vira;
    use super::*;

    #[tokio::test]
    async fn test_update_balance() {
        let mut vira = Vira::new().await;
        let _ = vira.contract.update().await;
    }

    #[tokio::test]
    async fn test_batch_check_mev_fallback() {
        // 这个测试用于验证降级机制
        // 注意：这个测试需要真实的合约环境才能运行
        // 在实际环境中，可以通过模拟合约调用失败来测试降级机制

        let vira = Vira::new().await;

        // 创建一些测试数据
        let test_inputs = vec![
            vec![ViraData::PoolReq {
                addr: Address::ZERO,
                version: U256::from(2),
                fee: U256::ZERO,
                fp: U256::ZERO,
                inIndex: U256::ZERO,
                outIndex: U256::from(1),
            }],
            vec![ViraData::PoolReq {
                addr: Address::ZERO,
                version: U256::from(2),
                fee: U256::ZERO,
                fp: U256::ZERO,
                inIndex: U256::from(1),
                outIndex: U256::ZERO,
            }],
        ];

        // 尝试调用batch_check_mev
        // 在真实环境中，如果批量调用失败，应该会自动降级为单个调用
        match vira.contract.batch_check_mev(test_inputs).await {
            Ok(results) => {
                println!("✅ 批量MEV检查成功，结果数量: {}", results.len());
                for (i, result) in results.iter().enumerate() {
                    println!("  结果 {}: gas={}, fee0_len={}, fee1_len={}",
                        i, result.gas, result.fee0.len(), result.fee1.len());
                }
            }
            Err(e) => {
                println!("❌ MEV检查失败: {:?}", e);
                // 在测试环境中，这可能是预期的，因为没有真实的合约环境
            }
        }
    }
}