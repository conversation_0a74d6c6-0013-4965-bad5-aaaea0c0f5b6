use std::collections::{BTreeMap, HashMap, HashSet};
use alloy::primitives::{Address, U256};
use std::cmp::Reverse;
use std::sync::Arc;

use crate::vira::{consts::{U_100, U_60}, pool::{DexPool, POOL}, status::pools::Pools};
use super::{cache::Cache, excluder::Excluder, value_calc, PoolIndex};

/// 池子索引管理器
/// 负责处理池子索引的添加、排序和查询
#[derive(Default)]
pub struct Index {
    /// 根据token的数量排序 token -> sort_list
    pub data: HashMap<Address, BTreeMap<(Reverse<U256>, Address), PoolIndex>>,
}

impl Index {
    /// 添加池子索引
    pub fn add(&mut self, pool: &POOL) {
        let data = pool.data();
        if data.tokens.len() > 2 { //TODO: 暂时不兼容balancer
            panic!("pool has more than 2 tokens: {:?}", data.addr);
        }

        if data.tokens.len() < 2 {
            println!("{:?}", data);
            panic!("pool has less 2 tokens: {:?}", data.addr);
        }

        for i in 0..2 {
            let tokens = &pool.data().tokens;
            let (in_token, out_token) = if i == 0 {
                (tokens[0].clone(), tokens[1].clone())
            } else {
                (tokens[1].clone(), tokens[0].clone())
            };

            let p = PoolIndex {
                addr: data.addr,
                in_index: in_token.index,
                out_index: out_token.index,
                in_token: in_token.addr,
                out_token: out_token.addr,
            };
            
            self.data.entry(tokens[i].addr)
                .or_insert_with(BTreeMap::new)
                .insert((Reverse(in_token.reserve), p.addr) , p);
        }
    }

    /// 获取指定代币的排名靠前的池子
    pub fn get_top_pairs_of_token<'a>(
        &'a self,
        addr: &Address,
        all_pools: &'a Arc<Pools>,
        exclude_pools: &Excluder,
        cache: Option<&'a Cache>,
        is_eoa_router: bool
    ) -> Vec<&'a PoolIndex> {
        let mut result = Vec::new();
        let max_count = 12;
        let sorted_pools = match cache
            .and_then(|c| c.cache_indexs_sorted.get(addr))
            .or_else(|| self.data.get(addr)) {
                Some(pools) => pools,
                None => return vec![]
            };

        for ((weight, _), pool) in sorted_pools.iter() {
            if exclude_pools.contains(&pool.addr) { continue; }
            //排除reserve为0的pool, 已经排好序，如果遇到0则后面的也是0
            if weight.0.is_zero() { break; }
            let d_data = all_pools.data.get(&pool.addr).expect("get pool error");
            let data = d_data.data();
            if is_eoa_router {
                if data.swap_way == crate::vira::pool::SwapWay::RouterByEoa {
                    result.push(pool);
                }
            } else if data.swap_way != crate::vira::pool::SwapWay::RouterByEoa {
                result.push(pool);
            }
            if result.len() >= max_count { break; }
        }
        result
    }

    //获取tokenA -> (tokenB, ..tokenN) 最大价值的路径
    pub fn get_path<'a>(
        &'a self,
        from: &Address,
        to : &HashSet<Address>,
        all_pools: &'a Arc<Pools>,
        cache: Option<&'a Cache>,
        exclude_pools : Option<&Vec<PoolIndex>>,
        is_eoa_router:bool
    ) -> Vec<&'a PoolIndex> {
        //同一条path如果存在sub pool, 则把主pool加入到exclude
        let mut exclude = Excluder::new();
        if let Some(c) = cache {
            exclude.insert(c.exclude_pool);
        }
        if let Some(c2) = exclude_pools {
            c2.iter().for_each(|p| {
                exclude.insert(p.addr);
            });
        }
        
        let top1 = self.get_top_pairs_of_token(from, all_pools, &exclude, cache, is_eoa_router);

        let mut results = Vec::with_capacity(200);
        
        if top1.is_empty() { return vec![]; }
        //第一层加入缓存
        exclude.batch_insert(top1.iter().map(|p| p.addr).collect());

        for l1 in top1 {
            //获取out token
            //let out1 = self.data.get(&l1.addr).unwrap().data().tokens[l1.out_index];
            let path1 = vec![l1];
            //todo!("添加忽略parent pool的逻辑，单条路径只使用一个parent pool");
            //所有index_sorted pool都只有2个token
            
            //let (_, out_token) = self.get_pool_token_from_index(&l1);
            if to.contains(&l1.out_token) {
                results.push(path1);
            } else {
                //filter_local.refresh_main(&path1);
                let top2 = self.get_top_pairs_of_token(&l1.out_token, all_pools, &exclude, cache, is_eoa_router);
                for l2 in top2 {
                    let mut path2 = path1.clone();
                    path2.push(l2);
                    //let (_, out_token) = self.get_pool_token_from_index(&l2);
                    if to.contains(&l2.out_token) {
                        results.push(path2);
                    } else {
                        //filter_local.refresh_main(&path2);
                        let top3 = self.get_top_pairs_of_token(&l2.out_token, all_pools, &exclude, cache, is_eoa_router);
                        for l3 in &top3 {
                            let mut path3 = path2.clone();
                            path3.push(l3);
                            //let (_, out_token) = self.get_pool_token_from_index(&l3);
                            if to.contains(&l3.out_token) {
                                results.push(path3);
                            }
                        }
                    }
                }
            }
        }

        if results.is_empty() { return vec![]; }

        if results.len() == 1 {
            return results.pop().unwrap();
        }
        
        //dbg!(&results.len());

        results.into_iter().max_by_key(|path| {
            let (_, mut lowest_value) = value_calc::ValueCalc::calc_lowest_value(path, all_pools, cache);
            for _ in 0..path.len() {
                lowest_value = lowest_value * U_100 / U_60;
            }
            lowest_value
        }).unwrap_or_default()



        // 找出results里修正后的lowest_value的最大值的Vec<PoolIndex>
        /*
        let mut max_index = 0;
        let mut max_lowest_value = U256::ZERO;
        for (index, path) in results.iter().enumerate() {
            let (_, mut lowest_value) = self.calc_lowest_value(path, None);
            
            // 根据路径长度调整lowest_value
            for _ in 0..path.len() {
                lowest_value = lowest_value * U256_100 / U256_60;
            }
            
            if lowest_value > max_lowest_value {
                max_lowest_value = lowest_value;
                max_index = index;
            }
        }
        // 返回修正后lowest_value最大的路径
        return results[max_index].clone();
            */

    }    

}