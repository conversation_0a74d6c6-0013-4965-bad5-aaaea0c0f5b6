use std::borrow::<PERSON><PERSON>;
use std::sync::Arc;
use alloy::primitives::{Address, U256};
use dashmap::DashMap;

use crate::vira::status::pools::Pools;
use crate::vira::{consts::U_2, pool::DexPool};
use crate::vira::pool::POOL;
use super::{PoolIndex, cache::Cache};
use colored::Colorize;

/// 价值计算器
/// 计算路径和池子的价值
pub struct ValueCalc;

impl ValueCalc {

    /// 获取池子索引的储备量
    pub fn get_reserves_by_index(
        all_pools: &Arc<Pools>,
        flow: &PoolIndex,
        cache: Option<&Cache>
    ) -> (U256, U256) {
        let (r_in, r_out) = if let Some(cache) = cache {
            if let Some(p) = cache.cache_data.get(&flow.addr) {
                p.reserve_by_index(flow.in_index, flow.out_index)
            } else {
                all_pools.data.get(&flow.addr).unwrap().reserve_by_index(flow.in_index, flow.out_index)
            }
        } else {
            all_pools.data.get(&flow.addr).unwrap().reserve_by_index(flow.in_index, flow.out_index)
        };
        (r_in, r_out)
    }

    /// 计算最低价值 -> (value of every pair, lowest value)
    /// 当前的逻辑是排主pool的最低价值
    /// TODO: 需要测试数值看是否需要排除主pool, 可以减少mev数量增加速度
    pub fn calc_lowest_value<T>(
        pools_flow: &[T],
        all_pools: &Arc<Pools>,
        cache: Option<&Cache>
    ) -> (Vec<U256>, U256)
    where
        T: Borrow<PoolIndex> + std::fmt::Debug,
    {
        let mut values = Vec::with_capacity(pools_flow.len());
        let mut lowest = U256::MAX;

        let mut pre_value = U256::ZERO;
        let mut pre_amount = U256::ZERO;

        for (i, flow) in pools_flow.iter().enumerate() {
            let flow: &PoolIndex = flow.borrow();
            let (r_in, r_out) = Self::get_reserves_by_index(all_pools, flow, cache);

            if r_out.is_zero() {
                //eprintln!("cache: {:?}", cache);
                eprintln!("pools_flow: {:?}", flow);
                eprintln!("r_in: {:?}, r_out: {:?}", r_in, r_out);
                panic!("r_out is zero");
                //lowest = U256::ZERO;
                //break;
            }

            //计算每个pool价值多少token_in
            let val = if i == 0 {
                r_in
            } else {
                if pre_amount.is_zero() {
                    //for debug
                    /*
                    pools_flow.iter().for_each(|p|{
                        let p : &PoolIndex = p.borrow();
                        //let pool = self.data.get(&p.addr).unwrap().data();
                        dbg!(self.data.get(&p.addr).unwrap().data());
                        let (debug_in, debug_out) = Self::get_reserves_by_index(&self.data, p, cache);
                        dbg!(&debug_in, &debug_out);
                    });
                    dbg!(&flow);
                     */
                    //panic!("pre_amount is zero");
                    eprintln!(
                        "[DEBUG] pre_amount is zero at index {}, pool: {}, in_index: {}, out_index: {}",
                        i, 
                        flow.addr, 
                        flow.in_index, 
                        flow.out_index
                    );
                    U256::ZERO
                } else {
                    pre_value * r_in / pre_amount
                }
            };

            //总价值需要x2
            let double = val * U_2;
            values.push(double);
            pre_value = val;
            pre_amount = r_out;

            if pools_flow.len() == 1 {
                lowest = double;
            } else if !cache.is_some() || cache.unwrap().exclude_pool != flow.addr {
                lowest = lowest.min(double);
            }
        }

        //self._display_pool_indexs(pools_flow, Some(format_ether(lowest)));
        //dbg!(&values);

        (values, lowest)
    }
}